import React from "react";
import { useAppStore } from "../../stores/useAppStore";
import { Welcome } from "./Welcome";
import { AccountSetup } from "./AccountSetup";
import { HealthAssessment } from "./HealthAssessment";
import { ApiSetup } from "./ApiSetup";
import { Complete } from "./Complete";

export const OnboardingFlow: React.FC = () => {
  const { currentStep, onboardingSteps } = useAppStore();

  const renderStep = () => {
    const step = onboardingSteps[currentStep];
    if (!step) return <Complete data-oid="b8zhvk7" />;

    switch (step.component) {
      case "Welcome":
        return <Welcome data-oid="6l6y:t." />;
      case "AccountSetup":
        return <AccountSetup data-oid="b2a6f6a" />;
      case "HealthAssessment":
        return <HealthAssessment data-oid="e92b25e" />;
      case "ApiSetup":
        return <ApiSetup data-oid="n5kknkq" />;
      case "Complete":
        return <Complete data-oid="xew_4wc" />;
      default:
        return <Welcome data-oid="umyviro" />;
    }
  };

  return <>{renderStep()}</>;
};
