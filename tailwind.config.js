/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      fontFamily: {
        sans: ['Inter', 'sans-serif'],
        serif: ['New York', 'serif'],
        mono: ['SF Mono', 'monospace'],
      },
      fontSize: {
        'large-title': '1.9375rem',
        'title-1': '1.5625rem',
        'title-2': '1.1875rem',
        body: '1.0625rem',
        caption: '0.6875rem',
      },
      lineHeight: {
        'large-title': '2.375rem',
        'title-1': '1.9375rem',
        'title-2': '1.5rem',
        body: '1.375rem',
        caption: '0.8125rem',
      },
      letterSpacing: {
        'large-title': '0.013em',
        'title-1': '0.006em',
        'title-2': '-0.024em',
        body: '-0.026em',
        caption: '0.006em',
      },
      spacing: {
        8: '2rem',
        16: '4rem',
        24: '6rem',
        32: '8rem',
        40: '10rem',
        80: '20rem',
        100: '25rem',
      },
      colors: {
        'primary-action': '#007AFF',
        'destructive-action': '#FF3B30',
        success: '#34C759',
        inactive: '#8E8E93',
      },
    },
  },
  plugins: [],
};
