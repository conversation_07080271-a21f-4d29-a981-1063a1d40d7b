import React from "react";
import { useAppStore } from "./stores/useAppStore";
import { OnboardingFlow } from "./components/onboarding/OnboardingFlow";
import { MainLayout } from "./components/layout/MainLayout";

function App() {
  const { user } = useAppStore();

  if (!user.isAuthenticated || !user.onboardingComplete) {
    return <OnboardingFlow data-oid="-.jh0p0" />;
  }

  return <MainLayout data-oid="belf5ja" />;
}

export default App;
