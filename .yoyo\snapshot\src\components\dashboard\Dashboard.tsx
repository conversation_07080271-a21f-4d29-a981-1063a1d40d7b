import React from "react";
import { Card } from "../ui/Card";
import { Button } from "../ui/Button";
import { useAppStore } from "../../stores/useAppStore";
import {
  MessageSquare,
  User,
  Shield,
  TrendingUp,
  Calendar,
  Heart,
  AlertCircle,
} from "lucide-react";

export const Dashboard: React.FC = () => {
  const { user, setCurrentView, chat } = useAppStore();

  const handleStartChat = () => {
    setCurrentView("chat");
  };

  const handleViewProfile = () => {
    setCurrentView("profile");
  };

  const stats = [
    {
      label: "Profile Complete",
      value: `${user.profile?.profileCompleteness || 0}%`,
      icon: User,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
    },
    {
      label: "Chat Messages",
      value: chat.messages.length.toString(),
      icon: MessageSquare,
      color: "text-green-600",
      bgColor: "bg-green-100",
    },
    {
      label: "Health Conditions",
      value: user.profile?.healthConditions.length.toString() || "0",
      icon: Heart,
      color: "text-red-600",
      bgColor: "bg-red-100",
    },
    {
      label: "Medications",
      value: user.profile?.medications.length.toString() || "0",
      icon: Shield,
      color: "text-purple-600",
      bgColor: "bg-purple-100",
    },
  ];

  const quickActions = [
    {
      title: "Chat with AI",
      description: "Get personalized Medicare guidance",
      icon: MessageSquare,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
      action: handleStartChat,
    },
    {
      title: "Complete Profile",
      description: "Add more health information",
      icon: User,
      color: "text-green-600",
      bgColor: "bg-green-100",
      action: handleViewProfile,
    },
    {
      title: "Find Plans",
      description: "Explore Medicare options",
      icon: Shield,
      color: "text-purple-600",
      bgColor: "bg-purple-100",
      action: () => setCurrentView("plans"),
    },
  ];

  const recentActivity = [
    {
      type: "chat",
      title: "Asked about Medicare Part D",
      time: "2 hours ago",
      icon: MessageSquare,
    },
    {
      type: "profile",
      title: "Updated medications list",
      time: "1 day ago",
      icon: User,
    },
    {
      type: "system",
      title: "Profile completeness increased",
      time: "2 days ago",
      icon: TrendingUp,
    },
  ];

  return (
    <div className="flex-1 overflow-y-auto p-6" data-oid="e.saani">
      <div className="max-w-7xl mx-auto space-y-6" data-oid="o:xaay:">
        {/* Welcome Section */}
        <div
          className="bg-gradient-to-r from-blue-600 to-blue-700 rounded-2xl p-8 text-white"
          data-oid="iwdjtl1"
        >
          <div className="flex items-center justify-between" data-oid="3t3a4am">
            <div data-oid="4r:pl0d">
              <h1 className="text-3xl font-bold mb-2" data-oid="9g3ga7:">
                Good morning, {user.profile?.firstName}!
              </h1>
              <p className="text-blue-100 text-lg" data-oid="zb4l7ux">
                Let's continue building your personalized Medicare profile.
              </p>
            </div>
            <div className="hidden md:block" data-oid="8jr2ip:">
              <div
                className="w-24 h-24 bg-white bg-opacity-20 rounded-full flex items-center justify-center"
                data-oid="y9dq.d-"
              >
                <Heart className="w-12 h-12 text-white" data-oid="bt0zk:9" />
              </div>
            </div>
          </div>
          <div className="mt-6" data-oid="u85gwmd">
            <Button
              onClick={handleStartChat}
              variant="secondary"
              size="lg"
              className="bg-white text-blue-600 hover:bg-gray-50"
              data-oid="l4_iap9"
            >
              Start a conversation
            </Button>
          </div>
        </div>

        {/* Stats Grid */}
        <div
          className="grid grid-cols-2 lg:grid-cols-4 gap-4"
          data-oid="7:x9zdn"
        >
          {stats.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <Card key={index} padding="md" data-oid="-83zj4_">
                <div className="flex items-center gap-3" data-oid="67yy-pi">
                  <div
                    className={`w-12 h-12 ${stat.bgColor} rounded-lg flex items-center justify-center`}
                    data-oid="g-muy0o"
                  >
                    <Icon
                      className={`w-6 h-6 ${stat.color}`}
                      data-oid="o3y8akd"
                    />
                  </div>
                  <div data-oid="6.9g6_n">
                    <p
                      className="text-2xl font-bold text-gray-900"
                      data-oid="po9:kqk"
                    >
                      {stat.value}
                    </p>
                    <p className="text-sm text-gray-600" data-oid="y4re4dk">
                      {stat.label}
                    </p>
                  </div>
                </div>
              </Card>
            );
          })}
        </div>

        <div
          className="grid grid-cols-1 lg:grid-cols-3 gap-6"
          data-oid="j1g1qve"
        >
          {/* Quick Actions */}
          <div className="lg:col-span-2" data-oid="ibog96h">
            <h2
              className="text-xl font-bold text-gray-900 mb-4"
              data-oid="lke:3sx"
            >
              Quick Actions
            </h2>
            <div className="grid gap-4" data-oid="eq4z68-">
              {quickActions.map((action, index) => {
                const Icon = action.icon;
                return (
                  <Card
                    key={index}
                    padding="md"
                    className="cursor-pointer hover:shadow-md transition-shadow"
                    onClick={action.action}
                    data-oid="37.9ezl"
                  >
                    <div className="flex items-center gap-4" data-oid="jdnwbp9">
                      <div
                        className={`w-12 h-12 ${action.bgColor} rounded-lg flex items-center justify-center`}
                        data-oid="-3d25nr"
                      >
                        <Icon
                          className={`w-6 h-6 ${action.color}`}
                          data-oid="j2j7gw0"
                        />
                      </div>
                      <div className="flex-1" data-oid="zewb9fj">
                        <h3
                          className="font-semibold text-gray-900"
                          data-oid="wgiy:yg"
                        >
                          {action.title}
                        </h3>
                        <p className="text-sm text-gray-600" data-oid="06yky_g">
                          {action.description}
                        </p>
                      </div>
                      <div className="text-gray-400" data-oid="1qp:vk0">
                        <svg
                          className="w-5 h-5"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                          data-oid="-l.:3kx"
                        >
                          <path
                            fillRule="evenodd"
                            d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                            clipRule="evenodd"
                            data-oid="ipqr.o4"
                          />
                        </svg>
                      </div>
                    </div>
                  </Card>
                );
              })}
            </div>
          </div>

          {/* Recent Activity */}
          <div data-oid="ds.q-q6">
            <h2
              className="text-xl font-bold text-gray-900 mb-4"
              data-oid="p0wbpx0"
            >
              Recent Activity
            </h2>
            <Card padding="md" data-oid="j40svn6">
              <div className="space-y-4" data-oid="18_359f">
                {recentActivity.map((activity, index) => {
                  const Icon = activity.icon;
                  return (
                    <div
                      key={index}
                      className="flex items-start gap-3"
                      data-oid="xivqm.v"
                    >
                      <div
                        className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center"
                        data-oid="dtqajbk"
                      >
                        <Icon
                          className="w-4 h-4 text-gray-600"
                          data-oid="k3.dg-x"
                        />
                      </div>
                      <div className="flex-1 min-w-0" data-oid="i.ts3ae">
                        <p
                          className="text-sm font-medium text-gray-900"
                          data-oid="e3-2qn4"
                        >
                          {activity.title}
                        </p>
                        <p className="text-xs text-gray-500" data-oid="_.xxzfj">
                          {activity.time}
                        </p>
                      </div>
                    </div>
                  );
                })}
              </div>
            </Card>

            {/* Profile Completion Reminder */}
            {(user.profile?.profileCompleteness || 0) < 100 && (
              <Card
                padding="md"
                className="mt-4 border-orange-200 bg-orange-50"
                data-oid="od2vsy0"
              >
                <div className="flex items-start gap-3" data-oid="d217rx0">
                  <AlertCircle
                    className="w-5 h-5 text-orange-600 mt-0.5"
                    data-oid="t9.:76h"
                  />

                  <div data-oid="4g2d3_:">
                    <h3
                      className="font-medium text-orange-900"
                      data-oid="klrwoap"
                    >
                      Complete Your Profile
                    </h3>
                    <p
                      className="text-sm text-orange-700 mt-1"
                      data-oid="zk.ht6."
                    >
                      Add more health information to get better AI
                      recommendations.
                    </p>
                    <Button
                      size="sm"
                      onClick={handleViewProfile}
                      className="mt-3 bg-orange-600 hover:bg-orange-700"
                      data-oid="qpuebg8"
                    >
                      Continue Setup
                    </Button>
                  </div>
                </div>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
