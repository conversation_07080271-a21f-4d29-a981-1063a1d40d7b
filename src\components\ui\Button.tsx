import React from "react";
import { cn } from "../../lib/utils";

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: "primary" | "secondary" | "outline" | "ghost";
  size?: "sm" | "md" | "lg";
  loading?: boolean;
  children: React.ReactNode;
}

export const Button: React.FC<ButtonProps> = ({
  variant = "primary",
  size = "md",
  loading = false,
  className,
  children,
  disabled,
  ...props
}) => {
  const baseClasses = [
    "inline-flex items-center justify-center rounded-lg font-semibold transition-all duration-200",
    "focus:outline-none focus:ring-2 focus:ring-offset-2",
    "disabled:cursor-not-allowed disabled:opacity-50",
  ];

  const variants = {
    primary: [
      "bg-primary-action text-white shadow-sm",
      "hover:bg-blue-700 focus:ring-blue-500",
      "active:bg-blue-800",
    ],

    secondary: [
      "bg-gray-100 text-gray-900 shadow-sm",
      "hover:bg-gray-200 focus:ring-gray-500",
      "active:bg-gray-300",
    ],

    outline: [
      "border border-gray-300 bg-white text-gray-700 shadow-sm",
      "hover:bg-gray-50 focus:ring-gray-500",
      "active:bg-gray-100",
    ],

    ghost: [
      "text-gray-700",
      "hover:bg-gray-100 focus:ring-gray-500",
      "active:bg-gray-200",
    ],
  };

  const sizes = {
    sm: "px-3 py-2 text-caption h-8",
    md: "px-4 py-2 text-body h-10",
    lg: "px-6 py-3 text-title-2 h-12",
  };

  return (
    <button
      className={cn(baseClasses, variants[variant], sizes[size], className)}
      disabled={disabled || loading}
      {...props}
      data-oid="e5iezsy"
    >
      {loading && (
        <svg
          className="w-4 h-4 mr-2 animate-spin"
          fill="none"
          viewBox="0 0 24 24"
          data-oid="b8cpneh"
        >
          <circle
            className="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="4"
            data-oid="326yb0l"
          />

          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            data-oid="r9i8q4-"
          />
        </svg>
      )}
      {children}
    </button>
  );
};
