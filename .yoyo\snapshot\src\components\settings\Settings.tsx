import React, { useState } from "react";
import { Card } from "../ui/Card";
import { Button } from "../ui/Button";
import { Input } from "../ui/Input";
import { useAppStore } from "../../stores/useAppStore";
import {
  Key,
  Shield,
  Bell,
  Palette,
  Download,
  Trash,
  ExternalLink,
  Eye,
  EyeOff,
} from "lucide-react";

export const Settings: React.FC = () => {
  const { geminiApiKey, setGeminiApi<PERSON>ey, user, ui } = useAppStore();
  const [showApiKey, setShowApiKey] = useState(false);
  const [newApiKey, setNewApiKey] = useState(geminiApiKey || "");
  const [isValidating, setIsValidating] = useState(false);

  const handleApiKeyUpdate = async () => {
    if (!newApiKey.trim()) return;

    setIsValidating(true);

    // Simulate validation
    setTimeout(() => {
      setGeminiApiKey(newApiKey);
      setIsValidating(false);
      alert("API key updated successfully!");
    }, 1000);
  };

  const handleExportData = () => {
    const data = {
      profile: user.profile,
      preferences: user.profile?.preferences,
      exportDate: new Date().toISOString(),
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], {
      type: "application/json",
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "xynai-profile-export.json";
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const settingSections = [
    {
      title: "AI Assistant Configuration",
      icon: Key,
      content: (
        <div className="space-y-4" data-oid="s:z4_16">
          <div data-oid="fuct9yf">
            <div
              className="flex justify-between items-center mb-2"
              data-oid="-hpwb2r"
            >
              <label
                className="block text-sm font-medium text-gray-700"
                data-oid="eijgdpb"
              >
                Gemini API Key
              </label>
              <a
                href="https://makersuite.google.com/app/apikey"
                target="_blank"
                rel="noopener noreferrer"
                className="text-sm text-blue-600 hover:text-blue-800 flex items-center gap-1"
                data-oid="xp8v01k"
              >
                Get API Key
                <ExternalLink className="w-3 h-3" data-oid="n3r878i" />
              </a>
            </div>
            <div className="relative" data-oid="ck1wxtj">
              <input
                type={showApiKey ? "text" : "password"}
                value={newApiKey}
                onChange={(e) => setNewApiKey(e.target.value)}
                placeholder="Enter your Gemini API key..."
                className="w-full px-4 py-2 pr-20 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                data-oid="86l:mki"
              />

              <div
                className="absolute right-2 top-1/2 transform -translate-y-1/2 flex gap-1"
                data-oid="vh:ea3."
              >
                <button
                  type="button"
                  onClick={() => setShowApiKey(!showApiKey)}
                  className="p-1 text-gray-400 hover:text-gray-600"
                  data-oid="xk6oxil"
                >
                  {showApiKey ? (
                    <EyeOff className="w-4 h-4" data-oid="kki-opc" />
                  ) : (
                    <Eye className="w-4 h-4" data-oid="x76.tlc" />
                  )}
                </button>
              </div>
            </div>
            <div className="flex gap-2 mt-3" data-oid="5fc61mq">
              <Button
                onClick={handleApiKeyUpdate}
                loading={isValidating}
                disabled={!newApiKey.trim() || newApiKey === geminiApiKey}
                size="sm"
                data-oid="wfw1qiu"
              >
                Update API Key
              </Button>
            </div>
            <p className="text-xs text-gray-600 mt-2" data-oid="8dcpxz2">
              🔒 Your API key is encrypted and stored locally. It's never shared
              with third parties.
            </p>
          </div>
        </div>
      ),
    },
    {
      title: "Privacy & Security",
      icon: Shield,
      content: (
        <div className="space-y-4" data-oid="3-_ujp8">
          <div className="flex items-center justify-between" data-oid="7:zs5s2">
            <div data-oid=".dxttza">
              <p className="font-medium text-gray-900" data-oid="5:94on0">
                Two-Factor Authentication
              </p>
              <p className="text-sm text-gray-600" data-oid="b2odrmo">
                Add an extra layer of security
              </p>
            </div>
            <Button variant="outline" size="sm" data-oid=".3cr43p">
              Enable
            </Button>
          </div>

          <div className="flex items-center justify-between" data-oid=":oi__4n">
            <div data-oid="8rmfijm">
              <p className="font-medium text-gray-900" data-oid="_b9.woi">
                Session Timeout
              </p>
              <p className="text-sm text-gray-600" data-oid="8sd10sz">
                Auto-logout after inactivity
              </p>
            </div>
            <select
              className="px-3 py-1 border border-gray-300 rounded text-sm"
              data-oid="_x1v3h2"
            >
              <option data-oid="090mc_7">30 minutes</option>
              <option data-oid="4q-vjfc">1 hour</option>
              <option data-oid="yy6ywfh">2 hours</option>
              <option data-oid="9cll2xg">Never</option>
            </select>
          </div>

          <div className="flex items-center justify-between" data-oid="xwdynsk">
            <div data-oid="jxp.u3m">
              <p className="font-medium text-gray-900" data-oid="ra9l.r1">
                Data Encryption
              </p>
              <p className="text-sm text-gray-600" data-oid="fkc59-t">
                All health data is encrypted at rest
              </p>
            </div>
            <span
              className="text-sm text-green-600 font-medium"
              data-oid="t-quff3"
            >
              Enabled
            </span>
          </div>
        </div>
      ),
    },
    {
      title: "Notifications",
      icon: Bell,
      content: (
        <div className="space-y-4" data-oid="-u7jz5l">
          <div className="flex items-center justify-between" data-oid="unyiz.z">
            <div data-oid="666d534">
              <p className="font-medium text-gray-900" data-oid="77xm3vh">
                Email Notifications
              </p>
              <p className="text-sm text-gray-600" data-oid="bdsq8t3">
                Health tips and plan updates
              </p>
            </div>
            <label
              className="relative inline-flex items-center cursor-pointer"
              data-oid="kqv3wmk"
            >
              <input
                type="checkbox"
                className="sr-only peer"
                checked={user.profile?.preferences.notifications}
                data-oid="9h1csm5"
              />

              <div
                className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"
                data-oid="bh9igx4"
              ></div>
            </label>
          </div>

          <div className="flex items-center justify-between" data-oid="bplpobf">
            <div data-oid=":gi2sfu">
              <p className="font-medium text-gray-900" data-oid="f8-bjk3">
                Push Notifications
              </p>
              <p className="text-sm text-gray-600" data-oid="mr1xuc4">
                Browser notifications for important updates
              </p>
            </div>
            <label
              className="relative inline-flex items-center cursor-pointer"
              data-oid="t818lhc"
            >
              <input
                type="checkbox"
                className="sr-only peer"
                defaultChecked
                data-oid="6rrpe83"
              />

              <div
                className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"
                data-oid="eaj7pu."
              ></div>
            </label>
          </div>

          <div className="flex items-center justify-between" data-oid="7cbctl6">
            <div data-oid="i68p9aa">
              <p className="font-medium text-gray-900" data-oid="_.5n1nr">
                Chat Notifications
              </p>
              <p className="text-sm text-gray-600" data-oid="fdi34_1">
                Alerts for new AI responses
              </p>
            </div>
            <label
              className="relative inline-flex items-center cursor-pointer"
              data-oid="mp8825_"
            >
              <input
                type="checkbox"
                className="sr-only peer"
                defaultChecked
                data-oid="nh_..om"
              />

              <div
                className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"
                data-oid="3zf6.om"
              ></div>
            </label>
          </div>
        </div>
      ),
    },
    {
      title: "Data Management",
      icon: Download,
      content: (
        <div className="space-y-4" data-oid="igcmtnw">
          <div className="flex items-center justify-between" data-oid="ungk_-w">
            <div data-oid="i74txfo">
              <p className="font-medium text-gray-900" data-oid="zfa8j:f">
                Export My Data
              </p>
              <p className="text-sm text-gray-600" data-oid="qki48:n">
                Download your profile and chat history
              </p>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={handleExportData}
              data-oid="9qrzr3u"
            >
              <Download className="w-4 h-4 mr-2" data-oid="7ktgjt2" />
              Export
            </Button>
          </div>

          <div className="flex items-center justify-between" data-oid="zez03ej">
            <div data-oid="w3ootpu">
              <p className="font-medium text-gray-900" data-oid="urwtx3v">
                Clear Chat History
              </p>
              <p className="text-sm text-gray-600" data-oid="i59ex_1">
                Remove all conversation history
              </p>
            </div>
            <Button variant="outline" size="sm" data-oid="2_w-5oc">
              Clear History
            </Button>
          </div>

          <div className="flex items-center justify-between" data-oid="dk6w1j-">
            <div data-oid="g8prhsu">
              <p
                className="font-medium text-gray-900 text-red-600"
                data-oid="vr-xej7"
              >
                Delete Account
              </p>
              <p className="text-sm text-gray-600" data-oid="fys1m8h">
                Permanently delete your account and data
              </p>
            </div>
            <Button
              variant="outline"
              size="sm"
              className="text-red-600 border-red-600 hover:bg-red-50"
              data-oid="ansmx0r"
            >
              <Trash className="w-4 h-4 mr-2" data-oid="ynmu:0k" />
              Delete
            </Button>
          </div>
        </div>
      ),
    },
  ];

  return (
    <div className="flex-1 overflow-y-auto p-6" data-oid="lfa5xa1">
      <div className="max-w-4xl mx-auto space-y-6" data-oid="cy_hv3q">
        <div className="mb-8" data-oid="u_ct99j">
          <h1
            className="text-2xl font-bold text-gray-900 mb-2"
            data-oid="xwg0w.b"
          >
            Settings
          </h1>
          <p className="text-gray-600" data-oid="::3tao0">
            Manage your account preferences and privacy settings
          </p>
        </div>

        <div className="space-y-6" data-oid="3:m_h1u">
          {settingSections.map((section, index) => {
            const Icon = section.icon;
            return (
              <Card key={index} padding="md" data-oid="ew0eum:">
                <div
                  className="flex items-center gap-3 mb-4"
                  data-oid="6jspf0_"
                >
                  <div
                    className="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center"
                    data-oid="d:d:qm-"
                  >
                    <Icon
                      className="w-4 h-4 text-gray-600"
                      data-oid="4c5p_x-"
                    />
                  </div>
                  <h2
                    className="text-lg font-semibold text-gray-900"
                    data-oid="wapc51k"
                  >
                    {section.title}
                  </h2>
                </div>
                {section.content}
              </Card>
            );
          })}
        </div>

        {/* Footer */}
        <Card padding="md" className="text-center" data-oid="wpqovui">
          <p className="text-sm text-gray-600 mb-2" data-oid="ww8tt76">
            Need help? Contact our support team
          </p>
          <div className="flex gap-2 justify-center" data-oid="xaqot2_">
            <Button variant="outline" size="sm" data-oid="bubrn.-">
              Help Center
            </Button>
            <Button variant="outline" size="sm" data-oid="oxkn9ec">
              Contact Support
            </Button>
          </div>
        </Card>
      </div>
    </div>
  );
};
