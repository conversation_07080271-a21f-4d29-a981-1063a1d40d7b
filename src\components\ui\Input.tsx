import React from "react";
import { cn } from "../../lib/utils";

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  helperText?: string;
}

export const Input: React.FC<InputProps> = ({
  label,
  error,
  helperText,
  className,
  id,
  ...props
}) => {
  const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;

  return (
    <div className="space-y-2">
      {label && (
        <label
          htmlFor={inputId}
          className="block text-body font-medium text-gray-700"
        >
          {label}
        </label>
      )}
      <input
        id={inputId}
        className={cn(
          "block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm",
          "placeholder-gray-400 text-body",
          "focus:outline-none focus:ring-2 focus:ring-primary-action focus:border-primary-action",
          "transition-colors duration-200",
          error && "border-destructive-action focus:ring-destructive-action focus:border-destructive-action",
          className,
        )}
        {...props}
      />

      {error && <p className="text-sm text-destructive-action">{error}</p>}
      {helperText && !error && (
        <p className="text-sm text-gray-500">{helperText}</p>
      )}
    </div>
  );
};
