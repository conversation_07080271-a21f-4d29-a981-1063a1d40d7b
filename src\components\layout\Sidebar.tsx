import React from "react";
import { useAppStore } from "../../stores/useAppStore";
import { cn } from "../../lib/utils";
import {
  MessageSquare,
  User,
  Shield,
  Settings,
  HelpCircle,
  Home,
  X,
  LogOut,
} from "lucide-react";

export const Sidebar: React.FC = () => {
  const { ui, user, toggleSidebar, setCurrentView, logout } = useAppStore();

  const navigation = [
    { name: "Dashboard", icon: Home, id: "dashboard" },
    { name: "Chat with AI", icon: MessageSquare, id: "chat" },
    { name: "My Profile", icon: User, id: "profile" },
    { name: "Medicare Plans", icon: Shield, id: "plans" },
    { name: "Settings", icon: Settings, id: "settings" },
    { name: "Help", icon: HelpCircle, id: "help" },
  ];

  const handleNavClick = (viewId: string) => {
    setCurrentView(viewId);
    if (window.innerWidth < 1024) {
      toggleSidebar();
    }
  };

  const handleLogout = () => {
    logout();
  };

  return (
    <>
      {/* Overlay */}
      {ui.sidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={toggleSidebar}
          data-oid="x8w77bu"
        />
      )}

      {/* Sidebar */}
      <div
        className={cn(
          "fixed inset-y-0 left-0 z-50 w-64 bg-white border-r border-gray-200 transform transition-transform duration-300 lg:translate-x-0 lg:static lg:z-0",
          ui.sidebarOpen ? "translate-x-0" : "-translate-x-full",
        )}
        data-oid="rq0u5tt"
      >
        <div className="flex flex-col h-full" data-oid="_3jmhg3">
          {/* Header */}
          <div
            className="flex items-center justify-between p-4 border-b border-gray-200"
            data-oid="yyvi4i-"
          >
            <div className="flex items-center gap-3" data-oid="j.amhbd">
              <div
                className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center"
                data-oid="5u5mcsr"
              >
                <span
                  className="text-white font-bold text-sm"
                  data-oid="9_f.cy1"
                >
                  X
                </span>
              </div>
              <span className="font-bold text-gray-900" data-oid="ex6m3ss">
                Xyn.ai
              </span>
            </div>
            <button
              onClick={toggleSidebar}
              className="lg:hidden p-1 rounded-md hover:bg-gray-100"
              data-oid="im933yj"
            >
              <X className="w-5 h-5 text-gray-500" data-oid="uqzvkz3" />
            </button>
          </div>

          {/* User Info */}
          <div className="p-4 border-b border-gray-200" data-oid=".h.9_w8">
            <div className="flex items-center gap-3" data-oid="g1okkz5">
              <div
                className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center"
                data-oid="ye.ouox"
              >
                <User className="w-5 h-5 text-gray-600" data-oid="5owrgpz" />
              </div>
              <div className="flex-1 min-w-0" data-oid="1nbs763">
                <p
                  className="text-sm font-medium text-gray-900 truncate"
                  data-oid="8q8r07k"
                >
                  {user.profile?.firstName} {user.profile?.lastName}
                </p>
                <p className="text-xs text-gray-500" data-oid="l.8amic">
                  Profile {user.profile?.profileCompleteness || 0}% complete
                </p>
              </div>
            </div>

            {/* Profile Completeness Bar */}
            <div className="mt-3" data-oid="n-l3h:y">
              <div
                className="w-full bg-gray-200 rounded-full h-1.5"
                data-oid="x8jvohr"
              >
                <div
                  className="bg-blue-600 h-1.5 rounded-full transition-all duration-300"
                  style={{
                    width: `${user.profile?.profileCompleteness || 0}%`,
                  }}
                  data-oid="ggfyher"
                />
              </div>
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 p-4 space-y-1" data-oid="b2-n0rb">
            {navigation.map((item) => {
              const Icon = item.icon;
              const isActive = ui.currentView === item.id;

              return (
                <button
                  key={item.id}
                  onClick={() => handleNavClick(item.id)}
                  className={cn(
                    "w-full flex items-center gap-3 px-3 py-2 text-left rounded-lg transition-colors",
                    isActive
                      ? "bg-blue-100 text-blue-700"
                      : "text-gray-700 hover:bg-gray-100",
                  )}
                  data-oid="kn18lqf"
                >
                  <Icon className="w-5 h-5" data-oid="igh9c2f" />
                  <span className="text-sm font-medium" data-oid="8qt8tba">
                    {item.name}
                  </span>
                </button>
              );
            })}
          </nav>

          {/* Footer */}
          <div className="p-4 border-t border-gray-200" data-oid="bzizmf:">
            <button
              onClick={handleLogout}
              className="w-full flex items-center gap-3 px-3 py-2 text-left rounded-lg text-gray-700 hover:bg-gray-100 transition-colors"
              data-oid="whvclog"
            >
              <LogOut className="w-5 h-5" data-oid="inws-6q" />
              <span className="text-sm font-medium" data-oid="w1.zab1">
                Sign out
              </span>
            </button>
          </div>
        </div>
      </div>
    </>
  );
};
