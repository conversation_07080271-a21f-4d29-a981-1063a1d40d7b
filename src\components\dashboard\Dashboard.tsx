import React from "react";
import { Card } from "../ui/Card";
import { Button } from "../ui/Button";
import { useAppStore } from "../../stores/useAppStore";
import {
  MessageSquare,
  User,
  Shield,
  TrendingUp,
  Calendar,
  Heart,
  AlertCircle,
} from "lucide-react";

export const Dashboard: React.FC = () => {
  const { user, setCurrentView, chat } = useAppStore();

  const handleStartChat = () => {
    setCurrentView("chat");
  };

  const handleViewProfile = () => {
    setCurrentView("profile");
  };

  const stats = [
    {
      label: "Profile Complete",
      value: `${user.profile?.profileCompleteness || 0}%`,
      icon: User,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
    },
    {
      label: "Chat Messages",
      value: chat.messages.length.toString(),
      icon: MessageSquare,
      color: "text-green-600",
      bgColor: "bg-green-100",
    },
    {
      label: "Health Conditions",
      value: user.profile?.healthConditions.length.toString() || "0",
      icon: Heart,
      color: "text-red-600",
      bgColor: "bg-red-100",
    },
    {
      label: "Medications",
      value: user.profile?.medications.length.toString() || "0",
      icon: Shield,
      color: "text-purple-600",
      bgColor: "bg-purple-100",
    },
  ];

  const quickActions = [
    {
      title: "Chat with AI",
      description: "Get personalized Medicare guidance",
      icon: MessageSquare,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
      action: handleStartChat,
    },
    {
      title: "Complete Profile",
      description: "Add more health information",
      icon: User,
      color: "text-green-600",
      bgColor: "bg-green-100",
      action: handleViewProfile,
    },
    {
      title: "Find Plans",
      description: "Explore Medicare options",
      icon: Shield,
      color: "text-purple-600",
      bgColor: "bg-purple-100",
      action: () => setCurrentView("plans"),
    },
  ];

  const recentActivity = [
    {
      type: "chat",
      title: "Asked about Medicare Part D",
      time: "2 hours ago",
      icon: MessageSquare,
    },
    {
      type: "profile",
      title: "Updated medications list",
      time: "1 day ago",
      icon: User,
    },
    {
      type: "system",
      title: "Profile completeness increased",
      time: "2 days ago",
      icon: TrendingUp,
    },
  ];

  return (
    <div className="flex-1" data-oid="jc43khh">
      <div className="max-w-5xl mx-auto space-y-6" data-oid="-megrc8">
        {/* Welcome Section (floating, saturated solid color, no gradient) */}
        <div className="elevated-card p-6 md:p-8" data-oid="d:cd_sz">
          <div className="flex items-center justify-between" data-oid="tn-ibxu">
            <div data-oid="pyqe8-w">
              <h1 className="large-title mb-2" data-oid="3u.ozk4">
                Good morning, {user.profile?.firstName}!
              </h1>
              <p className="text-gray-600 text-base" data-oid="0rmfgoi">
                Let’s continue building your personalized Medicare profile.
              </p>
            </div>
            <div className="hidden md:block" data-oid="9qe6vs4">
              <div className="w-24 h-24 rounded-2xl glass-card flex items-center justify-center" data-oid="iuoeagd">
                <Heart className="w-10 h-10 text-gray-700" data-oid="_ab6hnk" />
              </div>
            </div>
          </div>
          <div className="mt-6" data-oid="uijkj2l">
            <Button
              onClick={handleStartChat}
              variant="secondary"
              size="lg"
              className="bg-black text-white hover:bg-zinc-800"
              data-oid="96u9zf."
            >
              Start a conversation
            </Button>
          </div>
        </div>

        {/* Stats Grid */}
        <div
          className="grid grid-cols-2 lg:grid-cols-4 gap-4"
          data-oid="gbkeq6-"
        >
          {stats.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <Card key={index} padding="md" data-oid="tq7jy-e">
                <div className="flex items-center gap-3" data-oid="dnfbjs6">
                  <div
                    className={`w-12 h-12 ${stat.bgColor} rounded-lg flex items-center justify-center`}
                    data-oid="-d3vmjk"
                  >
                    <Icon
                      className={`w-6 h-6 ${stat.color}`}
                      data-oid="0-nkkjg"
                    />
                  </div>
                  <div data-oid="fhpizis">
                    <p
                      className="text-2xl font-bold text-gray-900"
                      data-oid="bn5c1_j"
                    >
                      {stat.value}
                    </p>
                    <p className="text-sm text-gray-600" data-oid="k5vj298">
                      {stat.label}
                    </p>
                  </div>
                </div>
              </Card>
            );
          })}
        </div>

        <div
          className="grid grid-cols-1 lg:grid-cols-3 gap-6"
          data-oid="_c..sag"
        >
          {/* Quick Actions */}
          <div className="lg:col-span-2" data-oid="ezt-mps">
            <h2
              className="text-xl font-bold text-gray-900 mb-4"
              data-oid="m9g3vqx"
            >
              Quick Actions
            </h2>
            <div className="grid gap-4" data-oid="6rd0:rd">
              {quickActions.map((action, index) => {
                const Icon = action.icon;
                return (
                  <Card
                    key={index}
                    padding="md"
                    className="cursor-pointer hover:shadow-md transition-shadow"
                    onClick={action.action}
                    data-oid="tqae309"
                  >
                    <div className="flex items-center gap-4" data-oid="7tyqig1">
                      <div
                        className={`w-12 h-12 ${action.bgColor} rounded-lg flex items-center justify-center`}
                        data-oid="hkhphj-"
                      >
                        <Icon
                          className={`w-6 h-6 ${action.color}`}
                          data-oid="u-zjsby"
                        />
                      </div>
                      <div className="flex-1" data-oid="b-6_sw9">
                        <h3
                          className="font-semibold text-gray-900"
                          data-oid="awo_cpu"
                        >
                          {action.title}
                        </h3>
                        <p className="text-sm text-gray-600" data-oid="9_-y8iy">
                          {action.description}
                        </p>
                      </div>
                      <div className="text-gray-400" data-oid="rr0yfrp">
                        <svg
                          className="w-5 h-5"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                          data-oid="92g10uc"
                        >
                          <path
                            fillRule="evenodd"
                            d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                            clipRule="evenodd"
                            data-oid="f5pk597"
                          />
                        </svg>
                      </div>
                    </div>
                  </Card>
                );
              })}
            </div>
          </div>

          {/* Recent Activity */}
          <div data-oid="lw74b7i">
            <h2
              className="text-xl font-bold text-gray-900 mb-4"
              data-oid="bxgnn60"
            >
              Recent Activity
            </h2>
            <Card padding="md" data-oid="v1ti.db">
              <div className="space-y-4" data-oid="9.ixufw">
                {recentActivity.map((activity, index) => {
                  const Icon = activity.icon;
                  return (
                    <div
                      key={index}
                      className="flex items-start gap-3"
                      data-oid="ej06r2s"
                    >
                      <div
                        className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center"
                        data-oid="f8k5hmw"
                      >
                        <Icon
                          className="w-4 h-4 text-gray-600"
                          data-oid="-vw46a7"
                        />
                      </div>
                      <div className="flex-1 min-w-0" data-oid="x4de68t">
                        <p
                          className="text-sm font-medium text-gray-900"
                          data-oid="-wyb7b6"
                        >
                          {activity.title}
                        </p>
                        <p className="text-xs text-gray-500" data-oid="r276dzm">
                          {activity.time}
                        </p>
                      </div>
                    </div>
                  );
                })}
              </div>
            </Card>

            {/* Profile Completion Reminder */}
            {(user.profile?.profileCompleteness || 0) < 100 && (
              <Card
                padding="md"
                className="mt-4 border-orange-200 bg-orange-50"
                data-oid="m:a-tja"
              >
                <div className="flex items-start gap-3" data-oid="pnjqyk7">
                  <AlertCircle
                    className="w-5 h-5 text-orange-600 mt-0.5"
                    data-oid="sk1g0ci"
                  />

                  <div data-oid="7b5s3qh">
                    <h3
                      className="font-medium text-orange-900"
                      data-oid="fd6ol1t"
                    >
                      Complete Your Profile
                    </h3>
                    <p
                      className="text-sm text-orange-700 mt-1"
                      data-oid="7oh4b89"
                    >
                      Add more health information to get better AI
                      recommendations.
                    </p>
                    <Button
                      size="sm"
                      onClick={handleViewProfile}
                      className="mt-3 bg-orange-600 hover:bg-orange-700"
                      data-oid="xp9n:t1"
                    >
                      Continue Setup
                    </Button>
                  </div>
                </div>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
