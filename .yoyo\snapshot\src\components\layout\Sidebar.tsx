import React from "react";
import { useAppStore } from "../../stores/useAppStore";
import { cn } from "../../lib/utils";
import {
  MessageSquare,
  User,
  Shield,
  Settings,
  HelpCircle,
  Home,
  X,
  LogOut,
} from "lucide-react";

export const Sidebar: React.FC = () => {
  const { ui, user, toggleSidebar, setCurrentView, logout } = useAppStore();

  const navigation = [
    { name: "Dashboard", icon: Home, id: "dashboard" },
    { name: "Chat with AI", icon: MessageSquare, id: "chat" },
    { name: "My Profile", icon: User, id: "profile" },
    { name: "Medicare Plans", icon: Shield, id: "plans" },
    { name: "Settings", icon: Settings, id: "settings" },
    { name: "Help", icon: HelpCircle, id: "help" },
  ];

  const handleNavClick = (viewId: string) => {
    setCurrentView(viewId);
    if (window.innerWidth < 1024) {
      toggleSidebar();
    }
  };

  const handleLogout = () => {
    logout();
  };

  return (
    <>
      {/* Overlay */}
      {ui.sidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={toggleSidebar}
          data-oid="2kdthiy"
        />
      )}

      {/* Sidebar */}
      <div
        className={cn(
          "fixed inset-y-0 left-0 z-50 w-64 bg-white border-r border-gray-200 transform transition-transform duration-300 lg:translate-x-0 lg:static lg:z-0",
          ui.sidebarOpen ? "translate-x-0" : "-translate-x-full",
        )}
        data-oid="2m6.z52"
      >
        <div className="flex flex-col h-full" data-oid="urq15u3">
          {/* Header */}
          <div
            className="flex items-center justify-between p-4 border-b border-gray-200"
            data-oid="khu3ld-"
          >
            <div className="flex items-center gap-3" data-oid="f8buysa">
              <div
                className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center"
                data-oid="045a8n_"
              >
                <span
                  className="text-white font-bold text-sm"
                  data-oid="ko64zg5"
                >
                  X
                </span>
              </div>
              <span className="font-bold text-gray-900" data-oid="nu:jjc2">
                Xyn.ai
              </span>
            </div>
            <button
              onClick={toggleSidebar}
              className="lg:hidden p-1 rounded-md hover:bg-gray-100"
              data-oid="jjcdwjl"
            >
              <X className="w-5 h-5 text-gray-500" data-oid="cgz:vdi" />
            </button>
          </div>

          {/* User Info */}
          <div className="p-4 border-b border-gray-200" data-oid="n.o46j5">
            <div className="flex items-center gap-3" data-oid="0207l_y">
              <div
                className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center"
                data-oid="1v4vqyz"
              >
                <User className="w-5 h-5 text-gray-600" data-oid="619v667" />
              </div>
              <div className="flex-1 min-w-0" data-oid="z3f2cie">
                <p
                  className="text-sm font-medium text-gray-900 truncate"
                  data-oid="zq0awsq"
                >
                  {user.profile?.firstName} {user.profile?.lastName}
                </p>
                <p className="text-xs text-gray-500" data-oid="wtz.nvr">
                  Profile {user.profile?.profileCompleteness || 0}% complete
                </p>
              </div>
            </div>

            {/* Profile Completeness Bar */}
            <div className="mt-3" data-oid="ierjfuo">
              <div
                className="w-full bg-gray-200 rounded-full h-1.5"
                data-oid="t:et_4o"
              >
                <div
                  className="bg-blue-600 h-1.5 rounded-full transition-all duration-300"
                  style={{
                    width: `${user.profile?.profileCompleteness || 0}%`,
                  }}
                  data-oid="9-xr_:1"
                />
              </div>
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 p-4 space-y-1" data-oid="hhl7_-l">
            {navigation.map((item) => {
              const Icon = item.icon;
              const isActive = ui.currentView === item.id;

              return (
                <button
                  key={item.id}
                  onClick={() => handleNavClick(item.id)}
                  className={cn(
                    "w-full flex items-center gap-3 px-3 py-2 text-left rounded-lg transition-colors",
                    isActive
                      ? "bg-blue-100 text-blue-700"
                      : "text-gray-700 hover:bg-gray-100",
                  )}
                  data-oid="ceo02pd"
                >
                  <Icon className="w-5 h-5" data-oid="1js_5.6" />
                  <span className="text-sm font-medium" data-oid="w5rnm1b">
                    {item.name}
                  </span>
                </button>
              );
            })}
          </nav>

          {/* Footer */}
          <div className="p-4 border-t border-gray-200" data-oid="ex3mple">
            <button
              onClick={handleLogout}
              className="w-full flex items-center gap-3 px-3 py-2 text-left rounded-lg text-gray-700 hover:bg-gray-100 transition-colors"
              data-oid="sqijiv0"
            >
              <LogOut className="w-5 h-5" data-oid="8vupfzb" />
              <span className="text-sm font-medium" data-oid="q.1yo9b">
                Sign out
              </span>
            </button>
          </div>
        </div>
      </div>
    </>
  );
};
