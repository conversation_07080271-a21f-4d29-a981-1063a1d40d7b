import React, { useState } from "react";
import { Card } from "../ui/Card";
import { Button } from "../ui/Button";
import { Input } from "../ui/Input";
import { useAppStore } from "../../stores/useAppStore";
import {
  Key,
  Shield,
  Bell,
  Palette,
  Download,
  Trash,
  ExternalLink,
  Eye,
  EyeOff,
} from "lucide-react";

export const Settings: React.FC = () => {
  const { geminiApiKey, setGeminiApi<PERSON>ey, user, ui } = useAppStore();
  const [showApiKey, setShowApiKey] = useState(false);
  const [newApiKey, setNewApiKey] = useState(geminiApiKey || "");
  const [isValidating, setIsValidating] = useState(false);

  const handleApiKeyUpdate = async () => {
    if (!newApiKey.trim()) return;

    setIsValidating(true);

    // Simulate validation
    setTimeout(() => {
      setGeminiApiKey(newApiKey);
      setIsValidating(false);
      alert("API key updated successfully!");
    }, 1000);
  };

  const handleExportData = () => {
    const data = {
      profile: user.profile,
      preferences: user.profile?.preferences,
      exportDate: new Date().toISOString(),
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], {
      type: "application/json",
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "xynai-profile-export.json";
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const settingSections = [
    {
      title: "AI Assistant Configuration",
      icon: Key,
      content: (
        <div className="space-y-4" data-oid=":k39rzz">
          <div data-oid="pgp7rk-">
            <div
              className="flex justify-between items-center mb-2"
              data-oid="bdyb8jm"
            >
              <label
                className="block text-sm font-medium text-gray-700"
                data-oid="hce2tl8"
              >
                Gemini API Key
              </label>
              <a
                href="https://makersuite.google.com/app/apikey"
                target="_blank"
                rel="noopener noreferrer"
                className="text-sm text-blue-600 hover:text-blue-800 flex items-center gap-1"
                data-oid="yshu:zg"
              >
                Get API Key
                <ExternalLink className="w-3 h-3" data-oid="z0a1jx:" />
              </a>
            </div>
            <div className="relative" data-oid="o3:u:p8">
              <input
                type={showApiKey ? "text" : "password"}
                value={newApiKey}
                onChange={(e) => setNewApiKey(e.target.value)}
                placeholder="Enter your Gemini API key..."
                className="w-full px-4 py-2 pr-20 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                data-oid="5ffqq58"
              />

              <div
                className="absolute right-2 top-1/2 transform -translate-y-1/2 flex gap-1"
                data-oid=".--c.yk"
              >
                <button
                  type="button"
                  onClick={() => setShowApiKey(!showApiKey)}
                  className="p-1 text-gray-400 hover:text-gray-600"
                  data-oid="u1s9b2d"
                >
                  {showApiKey ? (
                    <EyeOff className="w-4 h-4" data-oid="8sgplkv" />
                  ) : (
                    <Eye className="w-4 h-4" data-oid=":mu7z99" />
                  )}
                </button>
              </div>
            </div>
            <div className="flex gap-2 mt-3" data-oid="0h-92g0">
              <Button
                onClick={handleApiKeyUpdate}
                loading={isValidating}
                disabled={!newApiKey.trim() || newApiKey === geminiApiKey}
                size="sm"
                data-oid="pime3vk"
              >
                Update API Key
              </Button>
            </div>
            <p className="text-xs text-gray-600 mt-2" data-oid="d2q3r-o">
              🔒 Your API key is encrypted and stored locally. It's never shared
              with third parties.
            </p>
          </div>
        </div>
      ),
    },
    {
      title: "Privacy & Security",
      icon: Shield,
      content: (
        <div className="space-y-4" data-oid="ts59y1c">
          <div className="flex items-center justify-between" data-oid="2k9ystd">
            <div data-oid="kskdev:">
              <p className="font-medium text-gray-900" data-oid="lqwd4r.">
                Two-Factor Authentication
              </p>
              <p className="text-sm text-gray-600" data-oid="2l6ojs_">
                Add an extra layer of security
              </p>
            </div>
            <Button variant="outline" size="sm" data-oid="6.a_y9k">
              Enable
            </Button>
          </div>

          <div className="flex items-center justify-between" data-oid="..j76is">
            <div data-oid="p2gybgq">
              <p className="font-medium text-gray-900" data-oid="libdvv0">
                Session Timeout
              </p>
              <p className="text-sm text-gray-600" data-oid="b_8lmmi">
                Auto-logout after inactivity
              </p>
            </div>
            <select
              className="px-3 py-1 border border-gray-300 rounded text-sm"
              data-oid="qh-81ij"
            >
              <option data-oid=".53ls8k">30 minutes</option>
              <option data-oid="8u_sgqd">1 hour</option>
              <option data-oid="wj-obxk">2 hours</option>
              <option data-oid="vkac7w_">Never</option>
            </select>
          </div>

          <div className="flex items-center justify-between" data-oid="cv564pg">
            <div data-oid="s89cop5">
              <p className="font-medium text-gray-900" data-oid="5rrjk5w">
                Data Encryption
              </p>
              <p className="text-sm text-gray-600" data-oid="4fx41n9">
                All health data is encrypted at rest
              </p>
            </div>
            <span
              className="text-sm text-green-600 font-medium"
              data-oid="009ls4b"
            >
              Enabled
            </span>
          </div>
        </div>
      ),
    },
    {
      title: "Notifications",
      icon: Bell,
      content: (
        <div className="space-y-4" data-oid="r0v:2ae">
          <div className="flex items-center justify-between" data-oid="dlffkl_">
            <div data-oid="6aypcgd">
              <p className="font-medium text-gray-900" data-oid=":ybmzyr">
                Email Notifications
              </p>
              <p className="text-sm text-gray-600" data-oid=":3.a4q8">
                Health tips and plan updates
              </p>
            </div>
            <label
              className="relative inline-flex items-center cursor-pointer"
              data-oid="s1n72z6"
            >
              <input
                type="checkbox"
                className="sr-only peer"
                checked={user.profile?.preferences.notifications}
                data-oid="tlqqfyz"
              />

              <div
                className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"
                data-oid="3-5_kf:"
              ></div>
            </label>
          </div>

          <div className="flex items-center justify-between" data-oid="pbly-d.">
            <div data-oid="au9sf9x">
              <p className="font-medium text-gray-900" data-oid="4j26xye">
                Push Notifications
              </p>
              <p className="text-sm text-gray-600" data-oid="c2hxg6-">
                Browser notifications for important updates
              </p>
            </div>
            <label
              className="relative inline-flex items-center cursor-pointer"
              data-oid="cld.afk"
            >
              <input
                type="checkbox"
                className="sr-only peer"
                defaultChecked
                data-oid="p8z06o0"
              />

              <div
                className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"
                data-oid="1_5w1ze"
              ></div>
            </label>
          </div>

          <div className="flex items-center justify-between" data-oid="r5rpbpn">
            <div data-oid="new6tee">
              <p className="font-medium text-gray-900" data-oid="b:3m.xr">
                Chat Notifications
              </p>
              <p className="text-sm text-gray-600" data-oid="vh8ruyj">
                Alerts for new AI responses
              </p>
            </div>
            <label
              className="relative inline-flex items-center cursor-pointer"
              data-oid="_.ii1x_"
            >
              <input
                type="checkbox"
                className="sr-only peer"
                defaultChecked
                data-oid="k.v87cs"
              />

              <div
                className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"
                data-oid="xk.ao75"
              ></div>
            </label>
          </div>
        </div>
      ),
    },
    {
      title: "Data Management",
      icon: Download,
      content: (
        <div className="space-y-4" data-oid="d-wi4fo">
          <div className="flex items-center justify-between" data-oid="g8jc95i">
            <div data-oid="98vz.5u">
              <p className="font-medium text-gray-900" data-oid="z:w0ysm">
                Export My Data
              </p>
              <p className="text-sm text-gray-600" data-oid="6a2i3n:">
                Download your profile and chat history
              </p>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={handleExportData}
              data-oid="5f-jpz_"
            >
              <Download className="w-4 h-4 mr-2" data-oid="36tf6_f" />
              Export
            </Button>
          </div>

          <div className="flex items-center justify-between" data-oid="uaohkrk">
            <div data-oid="qn9v842">
              <p className="font-medium text-gray-900" data-oid="s64gh09">
                Clear Chat History
              </p>
              <p className="text-sm text-gray-600" data-oid="pixhdvp">
                Remove all conversation history
              </p>
            </div>
            <Button variant="outline" size="sm" data-oid="7lvxmew">
              Clear History
            </Button>
          </div>

          <div className="flex items-center justify-between" data-oid="c139sck">
            <div data-oid="928ifny">
              <p
                className="font-medium text-gray-900 text-red-600"
                data-oid="hvmciz6"
              >
                Delete Account
              </p>
              <p className="text-sm text-gray-600" data-oid="xguxa0_">
                Permanently delete your account and data
              </p>
            </div>
            <Button
              variant="outline"
              size="sm"
              className="text-red-600 border-red-600 hover:bg-red-50"
              data-oid=".7vpfc3"
            >
              <Trash className="w-4 h-4 mr-2" data-oid="l3yu0cw" />
              Delete
            </Button>
          </div>
        </div>
      ),
    },
  ];

  return (
    <div className="flex-1 overflow-y-auto p-6" data-oid="pa2qruw">
      <div className="max-w-4xl mx-auto space-y-6" data-oid="8y8gs6f">
        <div className="mb-8" data-oid="94m1iw_">
          <h1
            className="text-2xl font-bold text-gray-900 mb-2"
            data-oid="xlkbn:o"
          >
            Settings
          </h1>
          <p className="text-gray-600" data-oid=".nrbxn9">
            Manage your account preferences and privacy settings
          </p>
        </div>

        <div className="space-y-6" data-oid="3.xuu5d">
          {settingSections.map((section, index) => {
            const Icon = section.icon;
            return (
              <Card key={index} padding="md" data-oid="d8:-:9t">
                <div
                  className="flex items-center gap-3 mb-4"
                  data-oid="l:23egf"
                >
                  <div
                    className="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center"
                    data-oid="hekl5-d"
                  >
                    <Icon
                      className="w-4 h-4 text-gray-600"
                      data-oid="qr02l7z"
                    />
                  </div>
                  <h2
                    className="text-lg font-semibold text-gray-900"
                    data-oid="-i8vsdj"
                  >
                    {section.title}
                  </h2>
                </div>
                {section.content}
              </Card>
            );
          })}
        </div>

        {/* Footer */}
        <Card padding="md" className="text-center" data-oid="-l_j0at">
          <p className="text-sm text-gray-600 mb-2" data-oid="i-4.2.v">
            Need help? Contact our support team
          </p>
          <div className="flex gap-2 justify-center" data-oid="s7_4lha">
            <Button variant="outline" size="sm" data-oid="fpge.uq">
              Help Center
            </Button>
            <Button variant="outline" size="sm" data-oid="cb9shit">
              Contact Support
            </Button>
          </div>
        </Card>
      </div>
    </div>
  );
};
