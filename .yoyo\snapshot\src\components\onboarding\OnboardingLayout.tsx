import React from "react";
import { Card } from "../ui/Card";
import { useAppStore } from "../../stores/useAppStore";

interface OnboardingLayoutProps {
  children: React.ReactNode;
  title: string;
  description: string;
  currentStep: number;
  totalSteps: number;
}

export const OnboardingLayout: React.FC<OnboardingLayoutProps> = ({
  children,
  title,
  description,
  currentStep,
  totalSteps,
}) => {
  const progressPercentage = (currentStep / totalSteps) * 100;

  return (
    <div
      className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4"
      data-oid="mwm30_8"
    >
      <div className="w-full max-w-md" data-oid="kct.5ds">
        {/* Progress Bar */}
        <div className="mb-8" data-oid="voi51os">
          <div
            className="flex justify-between items-center mb-2"
            data-oid="z8fpjiz"
          >
            <span
              className="text-sm font-medium text-gray-600"
              data-oid="zo8.zk4"
            >
              Step {currentStep} of {totalSteps}
            </span>
            <span
              className="text-sm font-medium text-gray-600"
              data-oid="2r6n2__"
            >
              {Math.round(progressPercentage)}%
            </span>
          </div>
          <div
            className="w-full bg-gray-200 rounded-full h-2"
            data-oid="scpp5cy"
          >
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progressPercentage}%` }}
              data-oid="szbxlad"
            />
          </div>
        </div>

        <Card className="text-center" data-oid="o__.1-7">
          {/* Logo */}
          <div className="mb-8" data-oid="36ayc0c">
            <div
              className="w-16 h-16 bg-blue-600 rounded-2xl mx-auto flex items-center justify-center mb-4"
              data-oid="qvd7cy0"
            >
              <span className="text-white text-xl font-bold" data-oid="w07uidn">
                X
              </span>
            </div>
            <h1
              className="text-2xl font-bold text-gray-900 mb-2 tracking-tight"
              data-oid="na_5.e_"
            >
              {title}
            </h1>
            <p className="text-gray-600 leading-relaxed" data-oid="l61rzi6">
              {description}
            </p>
          </div>

          {children}
        </Card>
      </div>
    </div>
  );
};
