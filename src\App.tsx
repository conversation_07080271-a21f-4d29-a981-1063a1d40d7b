import React from "react";
import { useAppStore } from "./stores/useAppStore";
import { OnboardingFlow } from "./components/onboarding/OnboardingFlow";
import { MainLayout } from "./components/layout/MainLayout";

function App() {
  const { user } = useAppStore();

  if (!user.isAuthenticated || !user.onboardingComplete) {
    return <OnboardingFlow data-oid="w4y6ofj" />;
  }

  return <MainLayout data-oid="vupx2gw" />;
}

export default App;
