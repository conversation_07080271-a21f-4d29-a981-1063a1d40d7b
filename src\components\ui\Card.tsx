import React from "react";
import { cn } from "../../lib/utils";

interface CardProps {
  children: React.ReactNode;
  className?: string;
  padding?: "none" | "sm" | "md" | "lg";
}

export const Card: React.FC<CardProps> = ({
  children,
  className,
  padding = "md",
}) => {
  const paddingClasses = {
    none: "",
    sm: "p-8",
    md: "p-16",
    lg: "p-24",
  };

  return (
    <div
      className={cn(
        "bg-white rounded-xl border border-gray-200 shadow-sm",
        paddingClasses[padding],
        className,
      )}
      data-oid="ew9h7sr"
    >
      {children}
    </div>
  );
};
