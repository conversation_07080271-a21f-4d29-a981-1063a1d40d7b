import React from "react";
import { Sidebar } from "./Sidebar";
import { TopBar } from "./TopBar";
import { useAppStore } from "../../stores/useAppStore";
import { ChatInterface } from "../chat/ChatInterface";
import { Dashboard } from "../dashboard/Dashboard";
import { Profile } from "../profile/Profile";
import { Settings } from "../settings/Settings";

export const MainLayout: React.FC = () => {
  const { ui } = useAppStore();

  const renderCurrentView = () => {
    switch (ui.currentView) {
      case "dashboard":
        return <Dashboard data-oid="5mt0z7e" />;
      case "chat":
        return <ChatInterface data-oid="ri1ims-" />;
      case "profile":
        return <Profile data-oid="b_ma8ud" />;
      case "settings":
        return <Settings data-oid="j4qmdc4" />;
      case "plans":
        return (
          <div className="p-6" data-oid="g:6x5j-">
            <h1 data-oid="rv-139:">Medicare Plans (Coming Soon)</h1>
          </div>
        );

      case "help":
        return (
          <div className="p-6" data-oid="eo767-1">
            <h1 data-oid="-prm93e">Help & Support (Coming Soon)</h1>
          </div>
        );

      default:
        return <Dashboard data-oid="5s:_0k_" />;
    }
  };

  return (
    <div className="min-h-screen relative flex" data-oid=".8wzs0n">
      {/* Background layers */}
      <div className="app-bg" />

      <Sidebar data-oid="odi9hj-" />

      <div className="flex-1 flex flex-col min-w-0 relative z-10" data-oid="ajhrixk">
        <TopBar data-oid="n-7n-8y" />

        <main className="flex-1 overflow-y-auto p-4 md:p-6" data-oid="glqi6r-">
          <div className="max-w-5xl mx-auto space-y-6">
            <div className="glass-card p-4 md:p-6">
              {renderCurrentView()}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};
