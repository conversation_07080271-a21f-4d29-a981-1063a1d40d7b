import React from "react";
import { Sidebar } from "./Sidebar";
import { TopBar } from "./TopBar";
import { useAppStore } from "../../stores/useAppStore";
import { ChatInterface } from "../chat/ChatInterface";
import { Dashboard } from "../dashboard/Dashboard";
import { Profile } from "../profile/Profile";
import { Settings } from "../settings/Settings";

export const MainLayout: React.FC = () => {
  const { ui } = useAppStore();

  const renderCurrentView = () => {
    switch (ui.currentView) {
      case "dashboard":
        return <Dashboard data-oid="xvqvvk:" />;
      case "chat":
        return <ChatInterface data-oid=":9b8pan" />;
      case "profile":
        return <Profile data-oid="65k6u8o" />;
      case "settings":
        return <Settings data-oid="qppq1gf" />;
      case "plans":
        return (
          <div className="p-6" data-oid="pyx32cd">
            <h1 data-oid="o120xbu">Medicare Plans (Coming Soon)</h1>
          </div>
        );

      case "help":
        return (
          <div className="p-6" data-oid="yq4lyhy">
            <h1 data-oid="vq0w7ki">Help & Support (Coming Soon)</h1>
          </div>
        );

      default:
        return <Dashboard data-oid="jvsj2xt" />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex" data-oid="wdeh.nq">
      <Sidebar data-oid="b5j1ouo" />

      <div className="flex-1 flex flex-col min-w-0" data-oid=".c8jqbu">
        <TopBar data-oid="gj8w45z" />

        <main className="flex-1 overflow-hidden" data-oid="ykmmxf9">
          {renderCurrentView()}
        </main>
      </div>
    </div>
  );
};
