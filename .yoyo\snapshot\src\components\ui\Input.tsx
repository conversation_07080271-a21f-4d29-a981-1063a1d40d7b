import React from "react";
import { cn } from "../../lib/utils";

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  helperText?: string;
}

export const Input: React.FC<InputProps> = ({
  label,
  error,
  helperText,
  className,
  id,
  ...props
}) => {
  const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;

  return (
    <div className="space-y-2" data-oid="6iszbxp">
      {label && (
        <label
          htmlFor={inputId}
          className="block text-sm font-medium text-gray-700"
          data-oid="mg1ve6e"
        >
          {label}
        </label>
      )}
      <input
        id={inputId}
        className={cn(
          "block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm",
          "placeholder-gray-400 text-gray-900",
          "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
          "transition-colors duration-200",
          error && "border-red-500 focus:ring-red-500 focus:border-red-500",
          className,
        )}
        {...props}
        data-oid=":qtqv4-"
      />

      {error && (
        <p className="text-sm text-red-600" data-oid="_6hcwhy">
          {error}
        </p>
      )}
      {helperText && !error && (
        <p className="text-sm text-gray-500" data-oid="gxp09gh">
          {helperText}
        </p>
      )}
    </div>
  );
};
