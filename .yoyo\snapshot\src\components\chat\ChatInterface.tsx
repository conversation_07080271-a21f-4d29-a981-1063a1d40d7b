import React, { useState, useRef, useEffect } from "react";
import { Button } from "../ui/Button";
import { Card } from "../ui/Card";
import { useAppStore } from "../../stores/useAppStore";
import { generateAIResponse, extractHealthInfo } from "../../lib/ai-sdk";
import { generateId } from "../../lib/utils";
import { Send, Bot, User } from "lucide-react";

export const ChatInterface: React.FC = () => {
  const { chat, addMessage, setLoading, updateProfile, geminiApiKey, user } =
    useAppStore();

  const [inputValue, setInputValue] = useState("");
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [chat.messages]);

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!inputValue.trim() || chat.isLoading) return;

    if (!geminiApiKey) {
      alert("Please configure your Gemini API key in settings.");
      return;
    }

    const userMessage = {
      id: generateId(),
      content: inputValue.trim(),
      type: "user" as const,
      timestamp: new Date(),
    };

    addMessage(userMessage);
    setInputValue("");
    setLoading(true);

    try {
      const conversationHistory = chat.messages.map((msg) => ({
        role: msg.type === "user" ? "user" : "assistant",
        content: msg.content,
      }));

      conversationHistory.push({
        role: "user",
        content: userMessage.content,
      });

      const response = await generateAIResponse(conversationHistory);

      // Extract health information for profile building
      const extractedInfo = extractHealthInfo(response.content);

      // Update user profile if new health information is found
      if (Object.keys(extractedInfo).length > 0 && user.profile) {
        const profileUpdates: any = {};

        if (extractedInfo.medications) {
          const newMedications = [
            ...user.profile.medications,
            ...extractedInfo.medications,
          ].filter((med, index, arr) => arr.indexOf(med) === index);
          profileUpdates.medications = newMedications;
        }

        if (extractedInfo.conditions) {
          const newConditions = [
            ...user.profile.healthConditions,
            ...extractedInfo.conditions,
          ].filter((cond, index, arr) => arr.indexOf(cond) === index);
          profileUpdates.healthConditions = newConditions;
        }

        if (Object.keys(profileUpdates).length > 0) {
          // Increase profile completeness
          profileUpdates.profileCompleteness = Math.min(
            (user.profile.profileCompleteness || 0) + 5,
            100,
          );
          updateProfile(profileUpdates);
        }
      }

      const aiMessage = {
        id: generateId(),
        content: response.content,
        type: "ai" as const,
        timestamp: new Date(),
        metadata: {
          extracted_info: extractedInfo,
          usage: response.usage,
        },
      };

      addMessage(aiMessage);
    } catch (error) {
      console.error("Chat error:", error);
      const errorMessage = {
        id: generateId(),
        content:
          "I apologize, but I encountered an error processing your message. Please try again or check your API key configuration.",
        type: "ai" as const,
        timestamp: new Date(),
      };
      addMessage(errorMessage);
    } finally {
      setLoading(false);
      inputRef.current?.focus();
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  if (!geminiApiKey) {
    return (
      <div
        className="flex-1 flex items-center justify-center"
        data-oid="y:8hid1"
      >
        <Card className="text-center max-w-md" data-oid="n:9xeiv">
          <Bot
            className="w-12 h-12 text-gray-400 mx-auto mb-4"
            data-oid="t_sulla"
          />

          <h3
            className="text-lg font-semibold text-gray-900 mb-2"
            data-oid="it9.-nm"
          >
            AI Assistant Not Configured
          </h3>
          <p className="text-gray-600 mb-4" data-oid="c54hxem">
            Please configure your Gemini API key to start chatting with your AI
            health assistant.
          </p>
          <Button variant="outline" data-oid="imeo.6q">
            Configure API Key
          </Button>
        </Card>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full" data-oid="n8glpwy">
      {/* Chat Header */}
      <div className="border-b border-gray-200 p-4" data-oid=".:zo7ov">
        <div className="flex items-center gap-3" data-oid="1pjr6:g">
          <div
            className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center"
            data-oid="ek0u1a1"
          >
            <Bot className="w-5 h-5 text-blue-600" data-oid="68g7bh." />
          </div>
          <div data-oid="0xw3eq6">
            <h2
              className="font-medium font-sans text-xl tracking-tight
              text-gray-900"
              data-oid="n4.ekaz"
            >
              Xyn.ai Assistant
            </h2>
            <p
              className="text-sm text-md tracking-tight text-gray-600"
              data-oid=":.feoc-"
            >
              Your Medicare health guide
            </p>
          </div>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4" data-oid="b:oj9x4">
        {chat.messages.length === 0 && (
          <div className="text-center py-12" data-oid="ccmzosm">
            <Bot
              className="w-16 h-16 text-gray-300 mx-auto mb-4"
              data-oid="3o-loae"
            />

            <h3
              className="text-lg font-semibold text-gray-900 mb-2"
              data-oid="hkky:rq"
            >
              Start a conversation
            </h3>
            <p className="text-gray-600 mb-4" data-oid="u6n30-x">
              Ask me anything about Medicare, your health coverage, or get
              personalized recommendations.
            </p>
            <div className="text-left max-w-md mx-auto" data-oid="m8rop8f">
              <p
                className="text-sm font-medium text-gray-700 mb-2"
                data-oid="xhsrn22"
              >
                Try asking:
              </p>
              <ul
                className="text-sm text-gray-600 space-y-1"
                data-oid="kit.zmg"
              >
                <li data-oid="tvnoji8">
                  • "What Medicare plan is best for me?"
                </li>
                <li data-oid="_kpy_b9">• "How does Medicare Part D work?"</li>
                <li data-oid="tee54od">
                  • "I need help with prescription coverage"
                </li>
                <li data-oid="58kpbaj">• "What are my out-of-pocket costs?"</li>
              </ul>
            </div>
          </div>
        )}

        {chat.messages.map((message) => (
          <div
            key={message.id}
            className={`flex gap-3 ${
              message.type === "user" ? "justify-end" : "justify-start"
            }`}
            data-oid="et1d:.0"
          >
            {message.type === "ai" && (
              <div
                className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0"
                data-oid="r078.f8"
              >
                <Bot className="w-4 h-4 text-blue-600" data-oid="th3e612" />
              </div>
            )}

            <div
              className={`max-w-xs lg:max-w-md px-4 py-2 rounded-2xl ${
                message.type === "user"
                  ? "bg-blue-600 text-white"
                  : "bg-gray-100 text-gray-900"
              }`}
              data-oid="p_jpyem"
            >
              <p
                className="text-sm leading-relaxed whitespace-pre-wrap"
                data-oid="a_w7w69"
              >
                {message.content}
              </p>
              <p
                className={`text-xs mt-1 ${
                  message.type === "user" ? "text-blue-100" : "text-gray-500"
                }`}
                data-oid="s533zdy"
              >
                {formatTime(message.timestamp)}
              </p>
            </div>

            {message.type === "user" && (
              <div
                className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center flex-shrink-0"
                data-oid="gcmv9z8"
              >
                <User className="w-4 h-4 text-gray-600" data-oid="ylleat5" />
              </div>
            )}
          </div>
        ))}

        {chat.isLoading && (
          <div className="flex gap-3 justify-start" data-oid="x_xgjpy">
            <div
              className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center"
              data-oid="ef-l-kc"
            >
              <Bot className="w-4 h-4 text-blue-600" data-oid="694rl_d" />
            </div>
            <div
              className="bg-gray-100 rounded-2xl px-4 py-2"
              data-oid="cbu_osp"
            >
              <div className="flex space-x-1" data-oid="z39_h7y">
                <div
                  className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                  data-oid="_x59:xo"
                />

                <div
                  className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                  style={{ animationDelay: "0.1s" }}
                  data-oid="0h69k8l"
                />

                <div
                  className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                  style={{ animationDelay: "0.2s" }}
                  data-oid="d.7lm7p"
                />
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} data-oid="att4bo5" />
      </div>

      {/* Input Form */}
      <div className="border-t border-gray-200 p-4" data-oid="d69munn">
        <form
          onSubmit={handleSendMessage}
          className="flex gap-2"
          data-oid="gtjndbp"
        >
          <input
            ref={inputRef}
            type="text"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            placeholder="Ask me about Medicare, health plans, or coverage..."
            className="flex-1 px-4 py-2 border border-gray-300 rounded-full text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            disabled={chat.isLoading}
            data-oid="w1ny2w3"
          />

          <Button
            type="submit"
            size="sm"
            disabled={!inputValue.trim() || chat.isLoading}
            className="rounded-full px-4"
            data-oid="9w-8:4s"
          >
            <Send className="w-4 h-4" data-oid="oo9gpim" />
          </Button>
        </form>
      </div>
    </div>
  );
};
