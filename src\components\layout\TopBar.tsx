import React from "react";
import { useAppStore } from "../../stores/useAppStore";
import { <PERSON><PERSON>, <PERSON>, Search } from "lucide-react";
import { Button } from "../ui/Button";

export const TopBar: React.FC = () => {
  const { toggleSidebar, ui, user } = useAppStore();

  const getViewTitle = () => {
    switch (ui.currentView) {
      case "dashboard":
        return "Dashboard";
      case "chat":
        return "AI Assistant";
      case "profile":
        return "My Profile";
      case "settings":
        return "Settings";
      case "plans":
        return "Medicare Plans";
      case "help":
        return "Help & Support";
      default:
        return "Dashboard";
    }
  };

  return (
    <header
      className="glass-card h-16 flex items-center justify-between px-4 lg:px-6 mx-4 mt-4"
      data-oid="y7gtk6_"
    >
      <div className="flex items-center gap-4" data-oid="xzvnwem">
        <Button
          variant="ghost"
          size="sm"
          onClick={toggleSidebar}
          className="lg:hidden"
          data-oid="zf4ps9m"
        >
          <Menu className="w-5 h-5" data-oid="ne8rl1-" />
        </Button>

        <div data-oid="0u1xdru">
          <h1
            className="text-xl md:text-2xl font-semibold text-gray-900 tracking-tight"
            data-oid="0x206e0"
          >
            {getViewTitle()}
          </h1>
          {user.profile && (
            <p className="text-sm text-gray-600" data-oid="ks-:zml">
              Welcome back, {user.profile.firstName}
            </p>
          )}
        </div>
      </div>

      <div className="flex items-center gap-3" data-oid="9dng_tz">
        {/* Search (hidden on mobile) */}
        <div className="hidden md:flex items-center" data-oid="d2e5bed">
          <div className="relative" data-oid="srrfh61">
            <Search
              className="w-4 h-4 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2"
              data-oid=".-8b-yt"
            />

            <input
              type="text"
              placeholder="Search..."
              className="pl-10 pr-4 py-2 w-64 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              data-oid="9gqavzm"
            />
          </div>
        </div>

        {/* Notifications */}
        <Button variant="ghost" size="sm" data-oid="..ngw5f">
          <Bell className="w-5 h-5" data-oid="b7m_syv" />
        </Button>

        {/* Profile Avatar */}
        <div
          className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center"
          data-oid="u_z2iel"
        >
          <span
            className="text-blue-600 font-medium text-sm"
            data-oid="pu04ds-"
          >
            {user.profile?.firstName?.[0]}
            {user.profile?.lastName?.[0]}
          </span>
        </div>
      </div>
    </header>
  );
};
