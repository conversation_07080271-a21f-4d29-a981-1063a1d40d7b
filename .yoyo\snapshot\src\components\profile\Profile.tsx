import React from "react";
import { Card } from "../ui/Card";
import { Button } from "../ui/Button";
import { useAppStore } from "../../stores/useAppStore";
import {
  User,
  Mail,
  Calendar,
  Heart,
  Pill,
  Shield,
  Edit,
  Plus,
} from "lucide-react";

export const Profile: React.FC = () => {
  const { user, setCurrentView } = useAppStore();

  if (!user.profile) {
    return (
      <div
        className="flex-1 flex items-center justify-center"
        data-oid="40e0qlr"
      >
        <Card className="text-center" data-oid="g84fbft">
          <h2
            className="text-xl font-semibold text-gray-900 mb-2"
            data-oid="lwtp:ao"
          >
            No Profile Found
          </h2>
          <p className="text-gray-600" data-oid="viqhv7c">
            Please complete onboarding to set up your profile.
          </p>
        </Card>
      </div>
    );
  }

  const { profile } = user;

  const profileSections = [
    {
      title: "Basic Information",
      icon: User,
      items: [
        { label: "Name", value: `${profile.firstName} ${profile.lastName}` },
        { label: "Email", value: profile.email },
        {
          label: "Date of Birth",
          value: profile.dateOfBirth
            ? profile.dateOfBirth.toLocaleDateString()
            : "Not provided",
        },
        { label: "Medicare ID", value: profile.medicareId || "Not provided" },
      ],
    },
    {
      title: "Health Conditions",
      icon: Heart,
      items:
        profile.healthConditions.length > 0
          ? profile.healthConditions.map((condition) => ({
              label: "",
              value: condition,
            }))
          : [{ label: "", value: "No conditions added yet" }],
    },
    {
      title: "Current Medications",
      icon: Pill,
      items:
        profile.medications.length > 0
          ? profile.medications.map((medication) => ({
              label: "",
              value: medication,
            }))
          : [{ label: "", value: "No medications added yet" }],
    },
  ];

  return (
    <div className="flex-1 overflow-y-auto p-6" data-oid="kr-zu7j">
      <div className="max-w-4xl mx-auto space-y-6" data-oid="d1lr3xs">
        {/* Profile Header */}
        <Card padding="lg" data-oid="lipwiku">
          <div className="flex items-start justify-between" data-oid="o4id_a_">
            <div className="flex items-center gap-6" data-oid="a_e5byh">
              <div
                className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center"
                data-oid="vk6x:3."
              >
                <span
                  className="text-2xl font-bold text-blue-600"
                  data-oid="2i-w-8t"
                >
                  {profile.firstName[0]}
                  {profile.lastName[0]}
                </span>
              </div>
              <div data-oid="qht7dzm">
                <h1
                  className="text-2xl font-bold text-gray-900"
                  data-oid="t0m9b9v"
                >
                  {profile.firstName} {profile.lastName}
                </h1>
                <p
                  className="text-gray-600 flex items-center gap-2 mt-1"
                  data-oid=":j9pndj"
                >
                  <Mail className="w-4 h-4" data-oid="yjklbdk" />
                  {profile.email}
                </p>
                <p
                  className="text-gray-600 flex items-center gap-2 mt-1"
                  data-oid="blwavlv"
                >
                  <Calendar className="w-4 h-4" data-oid="-cj5c5g" />
                  Member since {profile.createdAt.toLocaleDateString()}
                </p>
              </div>
            </div>
            <Button variant="outline" data-oid="dwst_mc">
              <Edit className="w-4 h-4 mr-2" data-oid="2wnyiyn" />
              Edit Profile
            </Button>
          </div>

          {/* Profile Completeness */}
          <div className="mt-6 p-4 bg-gray-50 rounded-lg" data-oid="2bijth_">
            <div
              className="flex items-center justify-between mb-2"
              data-oid="hsvqv9a"
            >
              <span
                className="text-sm font-medium text-gray-700"
                data-oid="3b9_wie"
              >
                Profile Completeness
              </span>
              <span
                className="text-sm font-medium text-gray-900"
                data-oid="h4-baue"
              >
                {profile.profileCompleteness}%
              </span>
            </div>
            <div
              className="w-full bg-gray-200 rounded-full h-2"
              data-oid="swjpks1"
            >
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${profile.profileCompleteness}%` }}
                data-oid=":haoop2"
              />
            </div>
            <p className="text-xs text-gray-600 mt-2" data-oid="flun1cb">
              Complete your profile to get better AI recommendations
            </p>
          </div>
        </Card>

        {/* Profile Sections */}
        <div
          className="grid grid-cols-1 lg:grid-cols-2 gap-6"
          data-oid="_mca4oz"
        >
          {profileSections.map((section, index) => {
            const Icon = section.icon;
            return (
              <Card key={index} padding="md" data-oid="bp.hcq4">
                <div
                  className="flex items-center justify-between mb-4"
                  data-oid="6hfci28"
                >
                  <h2
                    className="text-lg font-semibold text-gray-900 flex items-center gap-2"
                    data-oid="e:xaw2j"
                  >
                    <Icon
                      className="w-5 h-5 text-gray-600"
                      data-oid="wwq3c3-"
                    />

                    {section.title}
                  </h2>
                  <Button variant="ghost" size="sm" data-oid="5xar35e">
                    <Plus className="w-4 h-4" data-oid="p:ei3tb" />
                  </Button>
                </div>

                <div className="space-y-3" data-oid="ve9gjet">
                  {section.items.map((item, itemIndex) => (
                    <div
                      key={itemIndex}
                      className="flex justify-between items-start"
                      data-oid="o478x.d"
                    >
                      {item.label && (
                        <span
                          className="text-sm font-medium text-gray-600 min-w-0 flex-1"
                          data-oid="g12lcdx"
                        >
                          {item.label}:
                        </span>
                      )}
                      <span
                        className={`text-sm text-gray-900 ${item.label ? "text-right" : ""} ${!item.label && item.value.includes("No ") ? "text-gray-500 italic" : ""}`}
                        data-oid="r9no:5d"
                      >
                        {item.value}
                      </span>
                    </div>
                  ))}
                </div>
              </Card>
            );
          })}
        </div>

        {/* Preferences */}
        <Card padding="md" data-oid="ds-ggn6">
          <h2
            className="text-lg font-semibold text-gray-900 flex items-center gap-2 mb-4"
            data-oid="_fxb8.8"
          >
            <Shield className="w-5 h-5 text-gray-600" data-oid="7nu8a07" />
            Privacy Preferences
          </h2>

          <div className="space-y-4" data-oid="mdxl-yk">
            <div
              className="flex items-center justify-between"
              data-oid="g_25wd9"
            >
              <div data-oid="7txahaz">
                <p className="font-medium text-gray-900" data-oid="83cvm0i">
                  Email Notifications
                </p>
                <p className="text-sm text-gray-600" data-oid="of2ji2l">
                  Receive updates about your health plan
                </p>
              </div>
              <label
                className="relative inline-flex items-center cursor-pointer"
                data-oid="zpybxy_"
              >
                <input
                  type="checkbox"
                  className="sr-only peer"
                  checked={profile.preferences.notifications}
                  data-oid="xjrnnu-"
                />

                <div
                  className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"
                  data-oid="68an5kr"
                ></div>
              </label>
            </div>

            <div
              className="flex items-center justify-between"
              data-oid="q-lgo5f"
            >
              <div data-oid="q:ayhid">
                <p className="font-medium text-gray-900" data-oid="jk.bwb1">
                  Data Sharing
                </p>
                <p className="text-sm text-gray-600" data-oid="73_vh1c">
                  Share anonymized data to improve AI recommendations
                </p>
              </div>
              <label
                className="relative inline-flex items-center cursor-pointer"
                data-oid="p731z53"
              >
                <input
                  type="checkbox"
                  className="sr-only peer"
                  checked={profile.preferences.dataSharing}
                  data-oid="a7.sly1"
                />

                <div
                  className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"
                  data-oid=".ikkn8f"
                ></div>
              </label>
            </div>
          </div>
        </Card>

        {/* Actions */}
        <div className="flex gap-3 justify-center" data-oid="2y150za">
          <Button
            onClick={() => setCurrentView("chat")}
            className="flex-1 max-w-xs"
            data-oid="_o-wh3d"
          >
            Continue Chatting to Build Profile
          </Button>
          <Button
            variant="outline"
            onClick={() => setCurrentView("settings")}
            className="flex-1 max-w-xs"
            data-oid="8gia83r"
          >
            Account Settings
          </Button>
        </div>
      </div>
    </div>
  );
};
