@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Design tokens inspired by modern mobile UIs: large titles, soft surfaces, crisp dividers */
:root {
  --surface: 255 255 255; /* white surfaces */
  --ink: 17 24 39; /* gray-900 */
  --muted-ink: 107 114 128; /* gray-500 */
  --card-radius: 20px;
  --soft-radius: 16px;
}

/* Background layers: photo/gradient images from resources without heavy color washes */
body {
  background-color: rgb(246 246 246);
}

/* App backdrop: stacked images with blur and subtle vignette */
.app-bg {
  position: fixed;
  inset: 0;
  pointer-events: none;
  z-index: 0;
}

.app-bg::before,
.app-bg::after {
  content: "";
  position: absolute;
  inset: 0;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}

/* Base image layer */
.app-bg::before {
  background-image: url('/resources/2576062b8e7f56ef08a00614e5cce9ef.jpg');
  filter: brightness(1) saturate(1.05);
}

/* Blur-and-vignette overlay to keep content legible */
.app-bg::after {
  backdrop-filter: blur(24px);
  -webkit-backdrop-filter: blur(24px);
  background: radial-gradient(1200px 600px at 50% 10%, rgba(255,255,255,0.5), rgba(255,255,255,0.35) 35%, rgba(255,255,255,0.15) 65%, rgba(255,255,255,0.0) 80%),
              radial-gradient(800px 400px at 10% 90%, rgba(255,255,255,0.35), transparent 70%),
              radial-gradient(800px 400px at 90% 90%, rgba(255,255,255,0.35), transparent 70%);
}

/* Glass card utility */
.glass-card {
  background: rgba(255, 255, 255, 0.72);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 0, 0, 0.06);
  border-radius: var(--card-radius);
}

/* Elevated card utility */
.elevated-card {
  background: rgb(var(--surface));
  border-radius: var(--card-radius);
  box-shadow: 0 10px 30px rgba(0,0,0,0.06);
}

/* Large mobile title */
.large-title {
  font-size: clamp(28px, 6vw, 44px);
  line-height: 1.05;
  font-weight: 800;
  letter-spacing: -0.02em;
  color: rgb(var(--ink));
}

/* Subtle divider */
.soft-divider {
  height: 1px;
  background: linear-gradient(to right, rgba(0,0,0,0), rgba(0,0,0,0.08), rgba(0,0,0,0));
}
