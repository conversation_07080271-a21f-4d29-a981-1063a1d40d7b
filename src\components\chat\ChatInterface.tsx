import React, { useState, useRef, useEffect } from "react";
import { Button } from "../ui/Button";
import { Card } from "../ui/Card";
import { useAppStore } from "../../stores/useAppStore";
import { generateAIResponse, extractHealthInfo } from "../../lib/ai-sdk";
import { generateId } from "../../lib/utils";
import { Send, Bot, User } from "lucide-react";

export const ChatInterface: React.FC = () => {
  const { chat, addMessage, setLoading, updateProfile, geminiApiKey, user } =
    useAppStore();

  const [inputValue, setInputValue] = useState("");
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [chat.messages]);

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!inputValue.trim() || chat.isLoading) return;

    if (!geminiApiKey) {
      alert("Please configure your Gemini API key in settings.");
      return;
    }

    const userMessage = {
      id: generateId(),
      content: inputValue.trim(),
      type: "user" as const,
      timestamp: new Date(),
    };

    addMessage(userMessage);
    setInputValue("");
    setLoading(true);

    try {
      const conversationHistory = chat.messages.map((msg) => ({
        role: msg.type === "user" ? "user" : "assistant",
        content: msg.content,
      }));

      conversationHistory.push({
        role: "user",
        content: userMessage.content,
      });

      const response = await generateAIResponse(conversationHistory);

      // Extract health information for profile building
      const extractedInfo = extractHealthInfo(response.content);

      // Update user profile if new health information is found
      if (Object.keys(extractedInfo).length > 0 && user.profile) {
        const profileUpdates: any = {};

        if (extractedInfo.medications) {
          const newMedications = [
            ...user.profile.medications,
            ...extractedInfo.medications,
          ].filter((med, index, arr) => arr.indexOf(med) === index);
          profileUpdates.medications = newMedications;
        }

        if (extractedInfo.conditions) {
          const newConditions = [
            ...user.profile.healthConditions,
            ...extractedInfo.conditions,
          ].filter((cond, index, arr) => arr.indexOf(cond) === index);
          profileUpdates.healthConditions = newConditions;
        }

        if (Object.keys(profileUpdates).length > 0) {
          // Increase profile completeness
          profileUpdates.profileCompleteness = Math.min(
            (user.profile.profileCompleteness || 0) + 5,
            100,
          );
          updateProfile(profileUpdates);
        }
      }

      const aiMessage = {
        id: generateId(),
        content: response.content,
        type: "ai" as const,
        timestamp: new Date(),
        metadata: {
          extracted_info: extractedInfo,
          usage: response.usage,
        },
      };

      addMessage(aiMessage);
    } catch (error) {
      console.error("Chat error:", error);
      const errorMessage = {
        id: generateId(),
        content:
          "I apologize, but I encountered an error processing your message. Please try again or check your API key configuration.",
        type: "ai" as const,
        timestamp: new Date(),
      };
      addMessage(errorMessage);
    } finally {
      setLoading(false);
      inputRef.current?.focus();
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  if (!geminiApiKey) {
    return (
      <div
        className="flex-1 flex items-center justify-center"
        data-oid="iou7hq4"
      >
        <Card className="text-center max-w-md" data-oid="yclyxrz">
          <Bot
            className="w-12 h-12 text-gray-400 mx-auto mb-4"
            data-oid="va1d9gi"
          />

          <h3
            className="text-title-1 font-semibold text-gray-900 mb-2"
            data-oid="kqg6og2"
          >
            AI Assistant Not Configured
          </h3>
          <p className="text-body text-gray-600 mb-4" data-oid="sf-yg:v">
            Please configure your Gemini API key to start chatting with your AI
            health assistant.
          </p>
          <Button variant="outline" data-oid="wkx3t71">
            Configure API Key
          </Button>
        </Card>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full" data-oid="mufmtd2">
      {/* Chat Header */}
      <div className="border-b border-gray-200 p-4" data-oid="kkpx6gl">
        <div className="flex items-center gap-3" data-oid="f3s_-x9">
          <div
            className="w-10 h-10 bg-primary-action rounded-full flex items-center justify-center"
            data-oid="3l0x3q_"
          >
            <Bot className="w-5 h-5 text-white" data-oid="g8gra6d" />
          </div>
          <div data-oid="-is2vqw">
            <h2
              className="font-medium font-sans text-title-2 tracking-tight
              text-gray-900"
              data-oid="kerts90"
            >
              Xyn.ai Assistant
            </h2>
            <p
              className="text-body tracking-tight text-gray-600"
              data-oid=".nr_riv"
            >
              Your Medicare health guide
            </p>
          </div>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4" data-oid="558e552">
        {chat.messages.length === 0 && (
          <div className="text-center py-12" data-oid="vdez:5m">
            <Bot
              className="w-16 h-16 text-gray-300 mx-auto mb-4"
              data-oid="3nscxe7"
            />

            <h3
              className="text-title-1 font-semibold text-gray-900 mb-2"
              data-oid="x-irey8"
            >
              Start a conversation
            </h3>
            <p className="text-body text-gray-600 mb-4" data-oid=":wsha42">
              Ask me anything about Medicare, your health coverage, or get
              personalized recommendations.
            </p>
            <div className="text-left max-w-md mx-auto" data-oid="6bns.hp">
              <p
                className="text-body font-medium text-gray-700 mb-2"
                data-oid="-qvizjo"
              >
                Try asking:
              </p>
              <ul
                className="text-body text-gray-600 space-y-1"
                data-oid="tq5buz7"
              >
                <li data-oid="76s2vzl">
                  • "What Medicare plan is best for me?"
                </li>
                <li data-oid="izs3nw0">• "How does Medicare Part D work?"</li>
                <li data-oid="60rsfr5">
                  • "I need help with prescription coverage"
                </li>
                <li data-oid="ibifzze">• "What are my out-of-pocket costs?"</li>
              </ul>
            </div>
          </div>
        )}

        {chat.messages.map((message) => (
          <div
            key={message.id}
            className={`flex gap-3 ${
              message.type === "user" ? "justify-end" : "justify-start"
            }`}
            data-oid="k.7q0sh"
          >
            {message.type === "ai" && (
              <div
                className="w-8 h-8 bg-primary-action rounded-full flex items-center justify-center flex-shrink-0"
                data-oid="82jf7nq"
              >
                <Bot className="w-4 h-4 text-white" data-oid=":uqe7pm" />
              </div>
            )}

            <div
              className={`max-w-xs lg:max-w-md px-4 py-2 rounded-2xl ${
                message.type === "user"
                  ? "bg-primary-action text-white"
                  : "bg-gray-100 text-gray-900"
              }`}
              data-oid="eql_c5r"
            >
              <p
                className="text-body leading-relaxed whitespace-pre-wrap"
                data-oid="nfm-3c1"
              >
                {message.content}
              </p>
              <p
                className={`text-caption mt-1 ${
                  message.type === "user" ? "text-blue-100" : "text-gray-500"
                }`}
                data-oid="bmru_7n"
              >
                {formatTime(message.timestamp)}
              </p>
            </div>

            {message.type === "user" && (
              <div
                className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center flex-shrink-0"
                data-oid="6ica1sq"
              >
                <User className="w-4 h-4 text-gray-600" data-oid="5n8lvis" />
              </div>
            )}
          </div>
        ))}

        {chat.isLoading && (
          <div className="flex gap-3 justify-start" data-oid="j5dsjot">
            <div
              className="w-8 h-8 bg-primary-action rounded-full flex items-center justify-center"
              data-oid="wia0a6i"
            >
              <Bot className="w-4 h-4 text-white" data-oid="xoefjrm" />
            </div>
            <div
              className="bg-gray-100 rounded-2xl px-4 py-2"
              data-oid="qvm.4cp"
            >
              <div className="flex space-x-1" data-oid="beme4gd">
                <div
                  className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                  data-oid="r-ikfo1"
                />

                <div
                  className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                  style={{ animationDelay: "0.1s" }}
                  data-oid=":a:-iil"
                />

                <div
                  className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                  style={{ animationDelay: "0.2s" }}
                  data-oid="lhbed2v"
                />
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} data-oid="cwmno4p" />
      </div>

      {/* Input Form */}
      <div className="border-t border-gray-200 p-4" data-oid="2lmqw_m">
        <form
          onSubmit={handleSendMessage}
          className="flex gap-2"
          data-oid="jhzycdj"
        >
          <input
            ref={inputRef}
            type="text"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            placeholder="Ask me about Medicare, health plans, or coverage..."
            className="flex-1 px-4 py-2 border border-gray-300 rounded-full text-body focus:outline-none focus:ring-2 focus:ring-primary-action focus:border-primary-action"
            disabled={chat.isLoading}
            data-oid="e7lqx_."
          />

          <Button
            type="submit"
            size="sm"
            disabled={!inputValue.trim() || chat.isLoading}
            className="rounded-full px-4"
            data-oid="l7-85u4"
          >
            <Send className="w-4 h-4" data-oid="ol32rh6" />
          </Button>
        </form>
      </div>
    </div>
  );
};
